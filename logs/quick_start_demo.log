2025-08-05 10:31:21,350 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 10:31:21,351 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 10:31:21,353 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 10:31:26,261 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 10:31:26,271 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: AlgorithmConfig.training() got an unexpected keyword argument 'sgd_minibatch_size'
2025-08-05 10:31:26,273 - __main__ - ERROR - quick_start.py:193 - Training failed: AlgorithmConfig.training() got an unexpected keyword argument 'sgd_minibatch_size'
2025-08-05 10:31:26,275 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 10:31:26,276 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 10:31:26,277 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:31:26,279 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 10:31:26,280 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 10:31:26,281 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:31:26,282 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 10:31:26,284 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 10:31:26,285 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 10:31:26,286 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 10:31:26,287 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 10:31:26,288 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 10:31:26,290 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 10:31:26,291 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 10:31:26,292 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 10:34:03,437 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 10:34:03,438 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 10:34:03,440 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 10:34:07,358 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 10:34:07,365 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: `rollouts` has been deprecated. Use `AlgorithmConfig.env_runners(..)` instead.
2025-08-05 10:34:07,366 - __main__ - ERROR - quick_start.py:193 - Training failed: `rollouts` has been deprecated. Use `AlgorithmConfig.env_runners(..)` instead.
2025-08-05 10:34:07,368 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 10:34:07,369 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 10:34:07,371 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:34:07,372 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 10:34:07,373 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 10:34:07,374 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:34:07,376 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 10:34:07,377 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 10:34:07,378 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 10:34:07,379 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 10:34:07,380 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 10:34:07,381 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 10:34:07,383 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 10:34:07,384 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 10:34:07,385 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 10:42:30,263 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 10:42:30,265 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 10:42:30,266 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 10:42:34,716 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 10:42:34,726 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 10:42:34,729 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: `evaluation_parallel_to_training` can only be done if `evaluation_num_env_runners` > 0! Try setting `config.evaluation_parallel_to_training` to False.
To suppress all validation errors, set `config.experimental(_validate_config=False)` at your own risk.
2025-08-05 10:42:36,386 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:333 - PPOAgent closed
2025-08-05 10:42:36,390 - __main__ - ERROR - quick_start.py:193 - Training failed: `evaluation_parallel_to_training` can only be done if `evaluation_num_env_runners` > 0! Try setting `config.evaluation_parallel_to_training` to False.
To suppress all validation errors, set `config.experimental(_validate_config=False)` at your own risk.
2025-08-05 10:42:36,395 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 10:42:36,398 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 10:42:36,401 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:42:36,404 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 10:42:36,406 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 10:42:36,409 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:42:36,412 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 10:42:36,415 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 10:42:36,417 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 10:42:36,420 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 10:42:36,423 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 10:42:36,425 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 10:42:36,428 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 10:42:36,431 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 10:42:36,433 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 10:48:09,744 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 10:48:09,745 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 10:48:09,746 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 10:48:14,347 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 10:48:14,357 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 10:48:14,368 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: Cannot use `custom_model` option with the new API stack (RLModule and Learner APIs)! `custom_model` is part of the ModelV2 API and Policy API, which are not compatible with the new API stack. You can either deactivate the new stack via `config.api_stack( enable_rl_module_and_learner=False)`,or use the new stack (incl. RLModule API) and implement your custom model as an RLModule.
To suppress all validation errors, set `config.experimental(_validate_config=False)` at your own risk.
2025-08-05 10:48:16,155 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:335 - PPOAgent closed
2025-08-05 10:48:16,159 - __main__ - ERROR - quick_start.py:193 - Training failed: Cannot use `custom_model` option with the new API stack (RLModule and Learner APIs)! `custom_model` is part of the ModelV2 API and Policy API, which are not compatible with the new API stack. You can either deactivate the new stack via `config.api_stack( enable_rl_module_and_learner=False)`,or use the new stack (incl. RLModule API) and implement your custom model as an RLModule.
To suppress all validation errors, set `config.experimental(_validate_config=False)` at your own risk.
2025-08-05 10:48:16,166 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 10:48:16,169 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 10:48:16,172 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:48:16,175 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 10:48:16,178 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 10:48:16,180 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:48:16,183 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 10:48:16,186 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 10:48:16,188 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 10:48:16,190 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 10:48:16,192 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 10:48:16,194 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 10:48:16,196 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 10:48:16,198 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 10:48:16,200 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 10:57:18,817 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 10:57:18,818 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 10:57:18,819 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 10:57:24,120 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 10:57:24,127 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 10:57:28,655 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: list index out of range
2025-08-05 10:57:30,470 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 10:57:30,473 - __main__ - ERROR - quick_start.py:193 - Training failed: list index out of range
2025-08-05 10:57:30,476 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 10:57:30,480 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 10:57:30,482 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:57:30,485 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 10:57:30,488 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 10:57:30,490 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 10:57:30,493 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 10:57:30,496 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 10:57:30,498 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 10:57:30,501 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 10:57:30,503 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 10:57:30,506 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 10:57:30,508 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 10:57:30,511 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 10:57:30,514 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 11:03:30,992 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:03:30,993 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:03:30,995 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:03:35,280 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:03:35,289 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:03:39,833 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: list index out of range
2025-08-05 11:03:41,545 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 11:03:41,546 - __main__ - ERROR - quick_start.py:193 - Training failed: list index out of range
2025-08-05 11:03:41,548 - __main__ - WARNING - quick_start.py:198 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 11:03:41,550 - __main__ - INFO - quick_start.py:204 - Running model evaluation...
2025-08-05 11:03:41,551 - __main__ - ERROR - quick_start.py:230 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:03:41,552 - __main__ - INFO - quick_start.py:231 - Skipping evaluation for demo
2025-08-05 11:03:41,553 - __main__ - INFO - quick_start.py:236 - Running backtesting...
2025-08-05 11:03:41,555 - __main__ - ERROR - quick_start.py:260 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:03:41,556 - __main__ - INFO - quick_start.py:261 - Skipping backtesting for demo
2025-08-05 11:03:41,557 - __main__ - INFO - quick_start.py:292 - ============================================================
2025-08-05 11:03:41,558 - __main__ - INFO - quick_start.py:293 - Quick start demo completed successfully!
2025-08-05 11:03:41,559 - __main__ - INFO - quick_start.py:294 - ============================================================
2025-08-05 11:03:41,561 - __main__ - INFO - quick_start.py:295 - Next steps:
2025-08-05 11:03:41,562 - __main__ - INFO - quick_start.py:296 - 1. Check results in 'results/' directory
2025-08-05 11:03:41,563 - __main__ - INFO - quick_start.py:297 - 2. Modify configs for your specific use case
2025-08-05 11:03:41,564 - __main__ - INFO - quick_start.py:298 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 11:03:41,565 - __main__ - INFO - quick_start.py:299 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 11:06:49,701 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:06:49,703 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:06:49,704 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:06:53,914 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:06:53,923 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:07:02,028 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:74 - Loading dataset: sample_dataset
2025-08-05 11:07:02,033 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:53 - Loading dataset 'sample_dataset' from data/processed
2025-08-05 11:07:02,038 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:62 - Loaded price data: (720, 4)
2025-08-05 11:07:02,041 - rl_ct.utils.data_loaders.disk_loader - INFO - disk_loader.py:70 - Loaded feature data: (720, 80)
2025-08-05 11:07:02,045 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:96 - Loaded data - Price: (720, 4), Features: (720, 80)
2025-08-05 11:07:02,048 - rl_ct.envs.crypto_trading_env - INFO - crypto_trading_env.py:70 - CryptoTradingEnv initialized with regime: training
2025-08-05 11:07:02,157 - rl_ct.models.base_model - INFO - base_model.py:61 - Initialized MLPModel on device: cuda
2025-08-05 11:07:03,366 - rl_ct.models.base_model - INFO - base_model.py:242 - Model moved to device: cuda
2025-08-05 11:07:03,368 - rl_ct.models.mlp - INFO - mlp.py:132 - MLPModel initialized with 703,749 parameters
2025-08-05 11:07:03,370 - rl_ct.models.rllib_models - INFO - rllib_models.py:187 - RLlibMLPModel initialized with 703,749 parameters
2025-08-05 11:07:04,550 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: Expected all tensors to be on the same device, but found at least two devices, cuda:0 and cpu! (when checking argument for argument mat1 in method wrapper_CUDA_addmm)
2025-08-05 11:07:06,151 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 11:07:06,153 - __main__ - ERROR - quick_start.py:194 - Training failed: Expected all tensors to be on the same device, but found at least two devices, cuda:0 and cpu! (when checking argument for argument mat1 in method wrapper_CUDA_addmm)
2025-08-05 11:07:06,155 - __main__ - WARNING - quick_start.py:199 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 11:07:06,157 - __main__ - INFO - quick_start.py:205 - Running model evaluation...
2025-08-05 11:07:06,159 - __main__ - ERROR - quick_start.py:231 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:07:06,160 - __main__ - INFO - quick_start.py:232 - Skipping evaluation for demo
2025-08-05 11:07:06,161 - __main__ - INFO - quick_start.py:237 - Running backtesting...
2025-08-05 11:07:06,163 - __main__ - ERROR - quick_start.py:261 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:07:06,164 - __main__ - INFO - quick_start.py:262 - Skipping backtesting for demo
2025-08-05 11:07:06,165 - __main__ - INFO - quick_start.py:293 - ============================================================
2025-08-05 11:07:06,166 - __main__ - INFO - quick_start.py:294 - Quick start demo completed successfully!
2025-08-05 11:07:06,168 - __main__ - INFO - quick_start.py:295 - ============================================================
2025-08-05 11:07:06,169 - __main__ - INFO - quick_start.py:296 - Next steps:
2025-08-05 11:07:06,170 - __main__ - INFO - quick_start.py:297 - 1. Check results in 'results/' directory
2025-08-05 11:07:06,171 - __main__ - INFO - quick_start.py:298 - 2. Modify configs for your specific use case
2025-08-05 11:07:06,172 - __main__ - INFO - quick_start.py:299 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 11:07:06,174 - __main__ - INFO - quick_start.py:300 - 4. Train on larger datasets with 'rl-ct train'
2025-08-05 11:16:34,583 - rl_ct.utils.logger - INFO - logger.py:142 - Logging setup complete. Level: INFO, Log file: logs/quick_start_demo.log
2025-08-05 11:16:34,585 - rl_ct.scripts.train - INFO - train.py:55 - Starting training with config: configs/training/quick_start.yaml
2025-08-05 11:16:34,586 - rl_ct.scripts.train - INFO - train.py:56 - Experiment name: quick_start_demo
2025-08-05 11:16:39,452 - rl_ct.agents.base_agent - INFO - base_agent.py:35 - Initialized PPOAgent on device: cuda
2025-08-05 11:16:39,459 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:53 - PPOAgent initialized
2025-08-05 11:16:44,261 - rl_ct.scripts.train - ERROR - train.py:141 - Training failed: list index out of range
2025-08-05 11:16:46,120 - rl_ct.agents.ppo_agent - INFO - ppo_agent.py:341 - PPOAgent closed
2025-08-05 11:16:46,123 - __main__ - ERROR - quick_start.py:194 - Training failed: list index out of range
2025-08-05 11:16:46,127 - __main__ - WARNING - quick_start.py:199 - Using dummy checkpoint for demo: checkpoints/quick_start_demo/dummy_model.zip
2025-08-05 11:16:46,130 - __main__ - INFO - quick_start.py:205 - Running model evaluation...
2025-08-05 11:16:46,133 - __main__ - ERROR - quick_start.py:231 - Evaluation failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:16:46,136 - __main__ - INFO - quick_start.py:232 - Skipping evaluation for demo
2025-08-05 11:16:46,139 - __main__ - INFO - quick_start.py:237 - Running backtesting...
2025-08-05 11:16:46,142 - __main__ - ERROR - quick_start.py:261 - Backtesting failed: Configuration file not found: configs/environment/default.yaml
2025-08-05 11:16:46,145 - __main__ - INFO - quick_start.py:262 - Skipping backtesting for demo
2025-08-05 11:16:46,147 - __main__ - INFO - quick_start.py:293 - ============================================================
2025-08-05 11:16:46,150 - __main__ - INFO - quick_start.py:294 - Quick start demo completed successfully!
2025-08-05 11:16:46,153 - __main__ - INFO - quick_start.py:295 - ============================================================
2025-08-05 11:16:46,155 - __main__ - INFO - quick_start.py:296 - Next steps:
2025-08-05 11:16:46,158 - __main__ - INFO - quick_start.py:297 - 1. Check results in 'results/' directory
2025-08-05 11:16:46,161 - __main__ - INFO - quick_start.py:298 - 2. Modify configs for your specific use case
2025-08-05 11:16:46,164 - __main__ - INFO - quick_start.py:299 - 3. Collect real market data using 'rl-ct collect-data'
2025-08-05 11:16:46,167 - __main__ - INFO - quick_start.py:300 - 4. Train on larger datasets with 'rl-ct train'
